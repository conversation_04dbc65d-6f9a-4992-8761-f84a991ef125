import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, Length, Matches } from 'class-validator';

/**
 * DTO cho việc xác thực OTP
 */
export class VerifyOtpDto {
  @ApiProperty({
    description: 'Mã OTP 6 số',
    example: '123456',
  })
  @IsString({ message: 'OTP phải là chuỗi' })
  @IsNotEmpty({ message: 'OTP không được để trống' })
  @Length(6, 6, { message: 'OTP phải có đúng 6 ký tự' })
  @Matches(/^\d{6}$/, { message: 'OTP phải là 6 chữ số' })
  otp: string;

  @ApiProperty({
    description: 'Token OTP để xác thực',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @IsString({ message: 'Token OTP phải là chuỗi' })
  @IsNotEmpty({ message: 'Token OTP không được để trống' })
  otpToken: string;
}
