import { Injectable, Logger } from '@nestjs/common';
import { TwoFactorAuthRepository } from '@modules/user/repositories';
import { TwoFactorAuth } from '@modules/user/entities';
import { TwoFactorAuthUtil } from '@shared/services/authenticator/two-factor-auth.util';
import { AppException, ErrorCode } from '@common/exceptions';
import { UserService } from './user.service';
import { TwoFactorAuthDetailDto } from '@modules/user/dto';

@Injectable()
export class TwoFactorAuthService {
  private readonly logger = new Logger(TwoFactorAuthService.name);
  private readonly issuer = 'RedAI'; // Tên ứng dụng hiển thị trong Google Authenticator

  constructor(
    private readonly twoFactorAuthRepository: TwoFactorAuthRepository,
    private readonly twoFactorAuthUtil: TwoFactorAuthUtil,
    private readonly userService: UserService,
  ) {}

  /**
   * Lấy trạng thái xác thực hai lớp của người dùng
   * @param userId ID của người dùng
   * @returns Trạng thái xác thực hai lớp
   */
  async getTwoFactorAuthStatus(userId: number): Promise<{
    otpSmsEnabled: boolean;
    otpEmailEnabled: boolean;
    googleAuthenticatorEnabled: boolean;
    isGoogleAuthenticatorConfirmed: boolean;
    is2FAEnabled: boolean;
  }> {
    const twoFactorAuth = await this.twoFactorAuthRepository.findOrCreate(userId);

    return {
      otpSmsEnabled: twoFactorAuth.otpSmsEnabled,
      otpEmailEnabled: twoFactorAuth.otpEmailEnabled,
      googleAuthenticatorEnabled: twoFactorAuth.googleAuthenticatorEnabled,
      isGoogleAuthenticatorConfirmed: twoFactorAuth.isGoogleAuthenticatorConfirmed,
      is2FAEnabled: twoFactorAuth.otpSmsEnabled || twoFactorAuth.otpEmailEnabled || twoFactorAuth.googleAuthenticatorEnabled,
    };
  }

  /**
   * Lấy thông tin chi tiết về cài đặt xác thực hai yếu tố của người dùng
   * @param userId ID của người dùng
   * @returns Thông tin chi tiết về cài đặt xác thực hai yếu tố
   */
  async getTwoFactorAuthDetail(userId: number): Promise<TwoFactorAuthDetailDto> {
    const twoFactorAuth = await this.twoFactorAuthRepository.findOrCreate(userId);

    const result = new TwoFactorAuthDetailDto();
    result.id = twoFactorAuth.id;
    result.userId = twoFactorAuth.userId;
    result.otpSmsEnabled = twoFactorAuth.otpSmsEnabled;
    result.otpEmailEnabled = twoFactorAuth.otpEmailEnabled;
    result.googleAuthenticatorEnabled = twoFactorAuth.googleAuthenticatorEnabled;
    result.isGoogleAuthenticatorConfirmed = twoFactorAuth.isGoogleAuthenticatorConfirmed;
    result.createdAt = twoFactorAuth.createdAt;
    result.updatedAt = twoFactorAuth.updatedAt;
    result.is2FAEnabled = twoFactorAuth.otpSmsEnabled || twoFactorAuth.otpEmailEnabled || twoFactorAuth.googleAuthenticatorEnabled;

    return result;
  }

  /**
   * Bật/tắt xác thực hai lớp qua SMS
   * @param userId ID của người dùng
   * @param enabled Trạng thái bật/tắt
   * @returns Trạng thái xác thực hai lớp sau khi cập nhật
   */
  async toggleSmsAuth(userId: number, enabled: boolean): Promise<TwoFactorAuth> {
    // Kiểm tra xem người dùng có số điện thoại không
    const user = await this.userService.findOne(userId);
    if (enabled && !user.phoneNumber) {
      throw new AppException(ErrorCode.VALIDATION_ERROR, 'Bạn cần cập nhật số điện thoại trước khi bật xác thực qua SMS');
    }

    return this.twoFactorAuthRepository.toggleSmsAuth(userId, enabled);
  }

  /**
   * Bật/tắt xác thực hai lớp qua email
   * @param userId ID của người dùng
   * @param enabled Trạng thái bật/tắt
   * @returns Trạng thái xác thực hai lớp sau khi cập nhật
   */
  async toggleEmailAuth(userId: number, enabled: boolean): Promise<TwoFactorAuth> {
    // Kiểm tra xem người dùng có email không
    const user = await this.userService.findOne(userId);
    if (enabled && !user.email) {
      throw new AppException(ErrorCode.VALIDATION_ERROR, 'Bạn cần cập nhật email trước khi bật xác thực qua email');
    }

    return this.twoFactorAuthRepository.toggleEmailAuth(userId, enabled);
  }

  /**
   * Tạo secret key và URL QR code cho Google Authenticator
   * @param userId ID của người dùng
   * @returns Secret key và URL QR code
   */
  async setupGoogleAuthenticator(userId: number): Promise<{
    secretKey: string;
    qrCodeUrl: string;
  }> {
    // Kiểm tra xem người dùng có email không
    const user = await this.userService.findOne(userId);
    if (!user.email) {
      throw new AppException(ErrorCode.VALIDATION_ERROR, 'Bạn cần cập nhật email trước khi thiết lập Google Authenticator');
    }

    // Tạo secret key mới
    const secretKey = this.twoFactorAuthUtil.createSecretKey();

    // Tạo URL otpauth
    const otpAuthUrl = this.twoFactorAuthUtil.generateOtpAuthUrl(
      secretKey,
      user.email,
      this.issuer,
    );

    // Tạo QR code
    const qrCodeUrl = await this.twoFactorAuthUtil.generateQrCodeDataUri(otpAuthUrl);

    // Lưu secret key vào database nhưng chưa bật Google Authenticator
    const twoFactorAuth = await this.twoFactorAuthRepository.findOrCreate(userId);
    twoFactorAuth.googleAuthenticatorSecret = secretKey;
    twoFactorAuth.googleAuthenticatorEnabled = false;
    twoFactorAuth.isGoogleAuthenticatorConfirmed = false;
    twoFactorAuth.updatedAt = Date.now();
    await this.twoFactorAuthRepository.save(twoFactorAuth);

    return {
      secretKey,
      qrCodeUrl,
    };
  }

  /**
   * Xác nhận cài đặt Google Authenticator
   * @param userId ID của người dùng
   * @param token Token từ Google Authenticator
   * @returns Trạng thái xác thực hai lớp sau khi cập nhật
   */
  async verifyAndEnableGoogleAuthenticator(userId: number, token: string): Promise<TwoFactorAuth> {
    // Lấy thông tin xác thực hai lớp
    const twoFactorAuth = await this.twoFactorAuthRepository.findByUserId(userId);
    if (!twoFactorAuth) {
      throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy cấu hình xác thực hai lớp');
    }

    // Kiểm tra xem đã thiết lập Google Authenticator chưa
    if (!twoFactorAuth.googleAuthenticatorSecret) {
      throw new AppException(ErrorCode.VALIDATION_ERROR, 'Bạn cần thiết lập Google Authenticator trước');
    }

    // Xác thực token
    const isValid = this.twoFactorAuthUtil.verifyToken(
      twoFactorAuth.googleAuthenticatorSecret,
      token,
    );

    if (!isValid) {
      throw new AppException(ErrorCode.VALIDATION_ERROR, 'Mã xác thực không chính xác');
    }

    // Bật Google Authenticator và đánh dấu đã xác nhận
    twoFactorAuth.googleAuthenticatorEnabled = true;
    twoFactorAuth.isGoogleAuthenticatorConfirmed = true;
    twoFactorAuth.updatedAt = Date.now();

    return this.twoFactorAuthRepository.save(twoFactorAuth);
  }

  /**
   * Bật/tắt Google Authenticator
   * @param userId ID của người dùng
   * @param enabled Trạng thái bật/tắt
   * @returns Trạng thái xác thực hai lớp sau khi cập nhật
   */
  async toggleGoogleAuthenticator(userId: number, enabled: boolean): Promise<TwoFactorAuth> {
    // Lấy thông tin xác thực hai lớp
    const twoFactorAuth = await this.twoFactorAuthRepository.findByUserId(userId);
    if (!twoFactorAuth) {
      throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy cấu hình xác thực hai lớp');
    }

    // Nếu đang bật Google Authenticator
    if (enabled) {
      // Kiểm tra xem đã thiết lập và xác nhận Google Authenticator chưa
      if (!twoFactorAuth.googleAuthenticatorSecret || !twoFactorAuth.isGoogleAuthenticatorConfirmed) {
        throw new AppException(ErrorCode.VALIDATION_ERROR, 'Bạn cần thiết lập và xác nhận Google Authenticator trước khi bật');
      }

      twoFactorAuth.googleAuthenticatorEnabled = true;
    } else {
      // Tắt Google Authenticator
      twoFactorAuth.googleAuthenticatorEnabled = false;
    }

    twoFactorAuth.updatedAt = Date.now();
    return this.twoFactorAuthRepository.save(twoFactorAuth);
  }

  /**
   * Tắt Google Authenticator
   * @param userId ID của người dùng
   * @returns Trạng thái xác thực hai lớp sau khi cập nhật
   */
  async disableGoogleAuthenticator(userId: number): Promise<TwoFactorAuth> {
    // Lấy thông tin xác thực hai lớp
    const twoFactorAuth = await this.twoFactorAuthRepository.findByUserId(userId);
    if (!twoFactorAuth) {
      throw new AppException(ErrorCode.RESOURCE_NOT_FOUND, 'Không tìm thấy cấu hình xác thực hai lớp');
    }

    // Tắt Google Authenticator
    twoFactorAuth.googleAuthenticatorEnabled = false;
    twoFactorAuth.isGoogleAuthenticatorConfirmed = false;
    twoFactorAuth.updatedAt = Date.now();

    return this.twoFactorAuthRepository.save(twoFactorAuth);
  }

  /**
   * Xác thực mã từ Google Authenticator
   * @param userId ID của người dùng
   * @param token Mã từ Google Authenticator
   * @returns True nếu mã hợp lệ, False nếu không
   */
  async verifyGoogleAuthenticator(userId: number, token: string): Promise<boolean> {
    try {
      this.logger.debug(`Bắt đầu xác thực Google Authenticator cho userId: ${userId}, token: ${token}`);

      // Lấy thông tin xác thực hai lớp
      const twoFactorAuth = await this.twoFactorAuthRepository.findByUserId(userId);
      if (!twoFactorAuth) {
        this.logger.error(`Không tìm thấy cấu hình xác thực hai lớp cho userId: ${userId}`);
        return false;
      }

      // Kiểm tra xem đã thiết lập Google Authenticator chưa
      if (!twoFactorAuth.googleAuthenticatorSecret) {
        this.logger.error(`Chưa thiết lập Google Authenticator cho userId: ${userId}`);
        return false;
      }

      this.logger.debug(`Tìm thấy secret cho userId: ${userId}, secret length: ${twoFactorAuth.googleAuthenticatorSecret.length}`);

      // Xác thực token
      const isValid = this.twoFactorAuthUtil.verifyToken(
        twoFactorAuth.googleAuthenticatorSecret,
        token,
      );

      this.logger.debug(`Kết quả xác thực cho userId: ${userId}, token: ${token}, isValid: ${isValid}`);
      return isValid;
    } catch (error) {
      this.logger.error(`Lỗi khi xác thực Google Authenticator: ${error.message}`, error.stack);
      return false;
    }
  }
}
