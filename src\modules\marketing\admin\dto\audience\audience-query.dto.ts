import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsOptional, IsString, IsPhoneNumber } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@/common/dto/query.dto';
import { IsInternationalPhone } from '@/shared/validators';



/**
 * DTO cho query parameters khi lấy danh sách audience
 */
export class AudienceQueryDto extends QueryDto {

  /**
   * Tìm kiếm tổng hợp trong tên, email và số điện thoại
   * @example "xin chào"
   */
  @ApiProperty({
    description: 'Tìm kiếm tổng hợp trong tên, email và số điện thoại. Nếu có search thì sẽ bỏ qua các trường name, email, phone riêng lẻ.',
    example: 'xin chào',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Từ khóa tìm kiếm phải là chuỗi' })
  declare search?: string;

  /**
   * Tìm kiếm theo tên
   * @example "Nguyễn"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo tên',
    example: 'Nguyễn',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên phải là chuỗi' })
  name?: string;

  /**
   * Tìm kiếm theo email
   * @example "example.com"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo email',
    example: 'example.com',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Email phải là chuỗi' })
  email?: string;

  /**
   * Tìm kiếm theo số điện thoại
   * @example "+84912345678"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo số điện thoại',
    example: '+84912345678',
    required: false,
  })
  @IsOptional()
  @IsInternationalPhone({ message: 'Số điện thoại không hợp lệ, phải là số điện thoại quốc tế hợp lệ (ví dụ: +84912345678)' })
  phone?: string;

  /**
   * Tìm kiếm theo tag ID
   * @example 1
   */
  @ApiProperty({
    description: 'Tìm kiếm theo tag ID',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsInt({ message: 'Tag ID phải là số nguyên' })
  @Type(() => Number)
  tagId?: number;

  /**
   * Tìm kiếm theo tên trường tùy chỉnh
   * @example "address"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo tên trường tùy chỉnh',
    example: 'address',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên trường tùy chỉnh phải là chuỗi' })
  customFieldName?: string;

  /**
   * Tìm kiếm theo giá trị trường tùy chỉnh
   * @example "Hanoi"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo giá trị trường tùy chỉnh',
    example: 'Hanoi',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Giá trị trường tùy chỉnh phải là chuỗi' })
  customFieldValue?: string;


}
