import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, IsBoolean } from 'class-validator';
import { Transform } from 'class-transformer';

/**
 * DTO cho query danh sách thông tin thiết bị
 */
export class DeviceInfoQueryDto {
  @ApiProperty({
    description: 'Trình duyệt',
    required: false,
  })
  @IsOptional()
  @IsString()
  browser?: string;

  @ApiProperty({
    description: 'Hệ điều hành',
    required: false,
  })
  @IsOptional()
  @IsString()
  operatingSystem?: string;

  @ApiProperty({
    description: 'Thiết bị đáng tin cậy',
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  isTrusted?: boolean;

  @ApiProperty({
    description: 'Địa chỉ IP',
    required: false,
  })
  @IsOptional()
  @IsString()
  ipAddress?: string;

  @ApiProperty({
    description: 'Từ ngày (timestamp)',
    required: false,
    example: 1640995200000,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  fromDate?: number;

  @ApiProperty({
    description: 'Đến ngày (timestamp)',
    required: false,
    example: 1640995200000,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  toDate?: number;

  @ApiProperty({
    description: 'Số trang',
    required: false,
    default: 1,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  page?: number = 1;

  @ApiProperty({
    description: 'Số bản ghi trên mỗi trang',
    required: false,
    default: 10,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  limit?: number = 10;
}

/**
 * DTO cho response thông tin thiết bị
 */
export class DeviceInfoResponseDto {
  @ApiProperty({
    description: 'ID của thiết bị',
    example: 'uuid-string',
  })
  id: string;

  @ApiProperty({
    description: 'ID của người dùng',
    example: 1,
  })
  userId: number;

  @ApiProperty({
    description: 'Fingerprint của thiết bị',
    example: 'device-fingerprint-hash',
  })
  fingerprint: string;

  @ApiProperty({
    description: 'Địa chỉ IP',
    example: '***********',
  })
  ipAddress: string;

  @ApiProperty({
    description: 'User agent',
    example: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  })
  userAgent: string;

  @ApiProperty({
    description: 'Trình duyệt',
    example: 'Chrome',
  })
  browser: string;

  @ApiProperty({
    description: 'Hệ điều hành',
    example: 'Windows 10',
  })
  operatingSystem: string;

  @ApiProperty({
    description: 'Thiết bị đáng tin cậy',
    example: true,
  })
  isTrusted: boolean;

  @ApiProperty({
    description: 'Thời gian đăng nhập gần nhất (timestamp)',
    example: 1640995200000,
  })
  lastLogin: number;

  @ApiProperty({
    description: 'Thời gian tạo (timestamp)',
    example: 1640995200000,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật (timestamp)',
    example: 1640995200000,
  })
  updatedAt: number;
}

/**
 * DTO cho cập nhật trạng thái tin cậy của thiết bị
 */
export class UpdateDeviceTrustDto {
  @ApiProperty({
    description: 'Thiết bị đáng tin cậy',
    example: true,
  })
  @IsBoolean()
  isTrusted: boolean;
}
