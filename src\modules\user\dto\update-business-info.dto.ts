import { ApiProperty } from '@nestjs/swagger';
import { IsString, MaxLength } from 'class-validator';
import { IsInternationalPhone } from '@/shared/validators';

/**
 * DTO cho việc cập nhật thông tin doanh nghiệp
 */
export class UpdateBusinessInfoDto {
  /**
   * Tên doanh nghiệp
   */
  @ApiProperty({
    description: 'Tên doanh nghiệp',
    example: 'Công ty TNHH ABC',
    required: true,
  })
  @IsString()
  @MaxLength(255)
  businessName: string;

  /**
   * Email doanh nghiệp
   */
  @ApiProperty({
    description: 'Email doanh nghiệp',
    example: '<EMAIL>',
    required: true,
  })
  @IsString()
  @MaxLength(100)
  businessEmail: string;

  /**
   * Số điện thoại doanh nghiệp
   */
  @ApiProperty({
    description: 'S<PERSON> điện thoại doanh nghiệp (định dạng quốc tế)',
    example: '+***********',
    required: true,
  })
  @IsString()
  @MaxLength(20)
  @IsInternationalPhone({ message: '<PERSON><PERSON> điện thoại doanh nghiệp không hợp lệ, phải là số điện thoại quốc tế hợp lệ (ví dụ: +***********)' })
  businessPhone: string;

  /**
   * URL giấy phép kinh doanh
   */
  @ApiProperty({
    description: 'URL giấy phép kinh doanh',
    example: 'https://example.com/certificate.pdf',
    required: true,
  })
  @IsString()
  @MaxLength(255)
  businessRegistrationCertificate: string;

  /**
   * Mã số thuế
   */
  @ApiProperty({
    description: 'Mã số thuế',
    example: '0123456789',
    required: true,
  })
  @IsString()
  @MaxLength(20)
  taxCode: string;

  /**
   * Tên người đại diện
   */
  @ApiProperty({
    description: 'Tên người đại diện',
    example: 'Nguyễn Văn A',
    required: true,
  })
  @IsString()
  @MaxLength(100)
  representativeName: string;

  /**
   * Vị trí người đại diện
   */
  @ApiProperty({
    description: 'Vị trí người đại diện',
    example: 'Giám đốc',
    required: true,
  })
  @IsString()
  @MaxLength(255)
  representativePosition: string;

  /**
   * Địa chỉ doanh nghiệp
   */
  @ApiProperty({
    description: 'Địa chỉ doanh nghiệp',
    example: '123 Nguyễn Văn Cừ, Quận 5, Hồ Chí Minh',
    required: true,
  })
  @IsString()
  @MaxLength(1000)
  businessAddress: string;
}
