import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { DeviceInfoRepository } from '../../repositories/device-info.repository';
import { DeviceInfoQueryDto, DeviceInfoResponseDto, UpdateDeviceTrustDto } from '../../dto/device-info.dto';
import { PaginatedResult, PaginationMeta } from '@common/response';
import { DeviceInfo } from '../../entities/device-info.entity';
import { AppException } from '@/common/exceptions/app.exception';
import { ErrorCode } from '@common/exceptions';

/**
 * Service cho quản lý thông tin thiết bị
 */
@Injectable()
export class DeviceInfoService {
  private readonly logger = new Logger(DeviceInfoService.name);

  constructor(
    private readonly deviceInfoRepository: DeviceInfoRepository,
  ) {}

  /**
   * <PERSON><PERSON>y danh sách thiết bị của người dùng hiện tại
   * @param userId ID của người dùng
   * @param queryDto Query parameters
   * @returns Danh sách thiết bị với phân trang
   */
  async getDeviceInfos(
    userId: number,
    queryDto: DeviceInfoQueryDto,
  ): Promise<PaginatedResult<DeviceInfoResponseDto>> {
    try {
      this.logger.debug(`Lấy danh sách thiết bị cho userId: ${userId}`);

      const {
        browser,
        operatingSystem,
        isTrusted,
        ipAddress,
        fromDate,
        toDate,
        page = 1,
        limit = 10,
      } = queryDto;

      // Tính toán offset
      const offset = (page - 1) * limit;

      // Xây dựng query builder
      const queryBuilder = this.deviceInfoRepository['repository']
        .createQueryBuilder('device')
        .where('device.userId = :userId', { userId });

      // Thêm các điều kiện filter
      if (browser) {
        queryBuilder.andWhere('device.browser LIKE :browser', { 
          browser: `%${browser}%` 
        });
      }

      if (operatingSystem) {
        queryBuilder.andWhere('device.operatingSystem LIKE :operatingSystem', { 
          operatingSystem: `%${operatingSystem}%` 
        });
      }

      if (typeof isTrusted === 'boolean') {
        queryBuilder.andWhere('device.isTrusted = :isTrusted', { isTrusted });
      }

      if (ipAddress) {
        queryBuilder.andWhere('device.ipAddress LIKE :ipAddress', { 
          ipAddress: `%${ipAddress}%` 
        });
      }

      if (fromDate) {
        queryBuilder.andWhere('device.createdAt >= :fromDate', { fromDate });
      }

      if (toDate) {
        queryBuilder.andWhere('device.createdAt <= :toDate', { toDate });
      }

      // Sắp xếp theo thời gian đăng nhập gần nhất
      queryBuilder.orderBy('device.lastLogin', 'DESC');

      // Lấy tổng số bản ghi
      const total = await queryBuilder.getCount();

      // Áp dụng phân trang
      queryBuilder.skip(offset).take(limit);

      // Lấy dữ liệu
      const devices = await queryBuilder.getMany();

      // Chuyển đổi sang DTO
      const items = devices.map(device => this.mapToResponseDto(device));

      // Tính toán thông tin phân trang
      const totalPages = Math.ceil(total / limit);
      const hasNext = page < totalPages;
      const hasPrev = page > 1;

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages,
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách thiết bị cho userId ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy chi tiết thiết bị theo ID
   * @param userId ID của người dùng
   * @param deviceId ID của thiết bị
   * @returns Chi tiết thiết bị
   */
  async getDeviceInfoById(
    userId: number,
    deviceId: string,
  ): Promise<DeviceInfoResponseDto | null> {
    try {
      this.logger.debug(`Lấy chi tiết thiết bị ${deviceId} cho userId: ${userId}`);

      const device = await this.deviceInfoRepository['repository']
        .createQueryBuilder('device')
        .where('device.id = :deviceId', { deviceId })
        .andWhere('device.userId = :userId', { userId })
        .getOne();

      if (!device) {
        return null;
      }

      return this.mapToResponseDto(device);
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy chi tiết thiết bị ${deviceId} cho userId ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Cập nhật trạng thái tin cậy của thiết bị
   * @param userId ID của người dùng
   * @param deviceId ID của thiết bị
   * @param updateDto Dữ liệu cập nhật
   * @returns Thiết bị đã cập nhật
   */
  async updateDeviceTrust(
    userId: number,
    deviceId: string,
    updateDto: UpdateDeviceTrustDto,
  ): Promise<DeviceInfoResponseDto> {
    try {
      this.logger.debug(`Cập nhật trạng thái tin cậy thiết bị ${deviceId} cho userId: ${userId}`);

      const device = await this.deviceInfoRepository['repository']
        .createQueryBuilder('device')
        .where('device.id = :deviceId', { deviceId })
        .andWhere('device.userId = :userId', { userId })
        .getOne();

      if (!device) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy thiết bị',
        );
      }

      // Cập nhật trạng thái tin cậy
      device.isTrusted = updateDto.isTrusted;
      device.updatedAt = Date.now();

      const updatedDevice = await this.deviceInfoRepository['repository'].save(device);

      return this.mapToResponseDto(updatedDevice);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi cập nhật trạng thái tin cậy thiết bị ${deviceId} cho userId ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Xóa thiết bị
   * @param userId ID của người dùng
   * @param deviceId ID của thiết bị
   */
  async deleteDevice(userId: number, deviceId: string): Promise<void> {
    try {
      this.logger.debug(`Xóa thiết bị ${deviceId} cho userId: ${userId}`);

      const device = await this.deviceInfoRepository['repository']
        .createQueryBuilder('device')
        .where('device.id = :deviceId', { deviceId })
        .andWhere('device.userId = :userId', { userId })
        .getOne();

      if (!device) {
        throw new AppException(
          ErrorCode.RESOURCE_NOT_FOUND,
          'Không tìm thấy thiết bị',
        );
      }

      await this.deviceInfoRepository['repository'].remove(device);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Lỗi khi xóa thiết bị ${deviceId} cho userId ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Lấy thống kê thiết bị của người dùng
   * @param userId ID của người dùng
   * @returns Thống kê thiết bị
   */
  async getDeviceStats(userId: number): Promise<{
    totalDevices: number;
    trustedDevices: number;
    untrustedDevices: number;
    browserStats: Array<{ browser: string; count: number }>;
    osStats: Array<{ os: string; count: number }>;
  }> {
    try {
      this.logger.debug(`Lấy thống kê thiết bị cho userId: ${userId}`);

      const devices = await this.deviceInfoRepository.findByUserId(userId);

      const totalDevices = devices.length;
      const trustedDevices = devices.filter(device => device.isTrusted).length;
      const untrustedDevices = totalDevices - trustedDevices;

      // Thống kê theo trình duyệt
      const browserStatsMap = new Map();
      devices.forEach(device => {
        const browser = device.browser || 'Unknown';
        browserStatsMap.set(browser, (browserStatsMap.get(browser) || 0) + 1);
      });

      const browserStats = Array.from(browserStatsMap.entries()).map(([browser, count]) => ({
        browser,
        count,
      }));

      // Thống kê theo hệ điều hành
      const osStatsMap = new Map();
      devices.forEach(device => {
        const os = device.operatingSystem || 'Unknown';
        osStatsMap.set(os, (osStatsMap.get(os) || 0) + 1);
      });

      const osStats = Array.from(osStatsMap.entries()).map(([os, count]) => ({
        os,
        count,
      }));

      return {
        totalDevices,
        trustedDevices,
        untrustedDevices,
        browserStats,
        osStats,
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy thống kê thiết bị cho userId ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Chuyển đổi entity sang response DTO
   * @param device Entity thiết bị
   * @returns Response DTO
   */
  private mapToResponseDto(device: DeviceInfo): DeviceInfoResponseDto {
    return {
      id: device.id,
      userId: device.userId,
      fingerprint: device.fingerprint,
      ipAddress: device.ipAddress,
      userAgent: device.userAgent,
      browser: device.browser,
      operatingSystem: device.operatingSystem,
      isTrusted: device.isTrusted,
      lastLogin: device.lastLogin,
      createdAt: device.createdAt,
      updatedAt: device.updatedAt,
    };
  }
}
