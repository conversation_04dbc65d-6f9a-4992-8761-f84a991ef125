import {
  Controller,
  Get,
  Put,
  Delete,
  Query,
  Param,
  Body,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiExtraModels,
  getSchemaPath,
} from '@nestjs/swagger';
import { DeviceInfoService } from '../service/device-info.service';
import { DeviceInfoQueryDto, DeviceInfoResponseDto, UpdateDeviceTrustDto } from '../../dto/device-info.dto';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { JwtAuthGuard } from '@/modules/auth/guards/jwt-auth.guard';
import { CurrentUser } from '@/modules/auth/decorators/current-user.decorator';
import { JWTPayload } from '@/modules/auth/interfaces/jwt-payload.interface';

@ApiTags('User - Device Info')
@ApiBearerAuth()
@ApiExtraModels(
  ApiResponseDto,
  PaginatedResult,
  DeviceInfoResponseDto,
  UpdateDeviceTrustDto,
)
@UseGuards(JwtAuthGuard)
@Controller('user/devices')
export class DeviceInfoController {
  constructor(
    private readonly deviceInfoService: DeviceInfoService,
  ) {}

  @Get()
  @ApiOperation({ 
    summary: 'Lấy danh sách thiết bị của người dùng hiện tại',
    description: 'API này cho phép người dùng xem danh sách các thiết bị đã đăng nhập với các bộ lọc và phân trang'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách thiết bị thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Lấy danh sách thiết bị thành công' },
            result: {
              allOf: [
                { $ref: getSchemaPath(PaginatedResult) },
                {
                  properties: {
                    items: {
                      type: 'array',
                      items: { $ref: getSchemaPath(DeviceInfoResponseDto) }
                    }
                  }
                }
              ]
            }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  async getDeviceInfos(
    @CurrentUser() user: JWTPayload,
    @Query() queryDto: DeviceInfoQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<DeviceInfoResponseDto>>> {
    const result = await this.deviceInfoService.getDeviceInfos(
      user.id,
      queryDto,
    );

    return ApiResponseDto.success(
      result,
      'Lấy danh sách thiết bị thành công',
    );
  }

  @Get('stats')
  @ApiOperation({ 
    summary: 'Lấy thống kê thiết bị của người dùng hiện tại',
    description: 'API này cung cấp thống kê tổng quan về các thiết bị của người dùng'
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy thống kê thiết bị thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Lấy thống kê thiết bị thành công' },
            result: {
              type: 'object',
              properties: {
                totalDevices: { type: 'number', example: 5, description: 'Tổng số thiết bị' },
                trustedDevices: { type: 'number', example: 3, description: 'Số thiết bị đáng tin cậy' },
                untrustedDevices: { type: 'number', example: 2, description: 'Số thiết bị chưa tin cậy' },
                browserStats: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      browser: { type: 'string', example: 'Chrome', description: 'Tên trình duyệt' },
                      count: { type: 'number', example: 3, description: 'Số lượng thiết bị' }
                    }
                  },
                  description: 'Thống kê theo trình duyệt'
                },
                osStats: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      os: { type: 'string', example: 'Windows 10', description: 'Tên hệ điều hành' },
                      count: { type: 'number', example: 2, description: 'Số lượng thiết bị' }
                    }
                  },
                  description: 'Thống kê theo hệ điều hành'
                }
              }
            }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  async getDeviceStats(
    @CurrentUser() user: JwtPayload,
  ): Promise<ApiResponseDto<any>> {
    const result = await this.deviceInfoService.getDeviceStats(user.id);

    return ApiResponseDto.success(
      result,
      'Lấy thống kê thiết bị thành công',
    );
  }

  @Get(':id')
  @ApiOperation({ 
    summary: 'Lấy chi tiết thiết bị theo ID',
    description: 'API này cho phép người dùng xem chi tiết một thiết bị cụ thể'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của thiết bị',
    type: 'string',
    example: 'uuid-string',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy chi tiết thiết bị thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Lấy chi tiết thiết bị thành công' },
            result: { $ref: getSchemaPath(DeviceInfoResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy thiết bị' })
  async getDeviceInfoById(
    @CurrentUser() user: JwtPayload,
    @Param('id') deviceId: string,
  ): Promise<ApiResponseDto<DeviceInfoResponseDto>> {
    const result = await this.deviceInfoService.getDeviceInfoById(
      user.id,
      deviceId,
    );

    if (!result) {
      return ApiResponseDto.error(
        404,
        'Không tìm thấy thiết bị',
      );
    }

    return ApiResponseDto.success(
      result,
      'Lấy chi tiết thiết bị thành công',
    );
  }

  @Put(':id/trust')
  @ApiOperation({ 
    summary: 'Cập nhật trạng thái tin cậy của thiết bị',
    description: 'API này cho phép người dùng đánh dấu thiết bị là đáng tin cậy hoặc không'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của thiết bị',
    type: 'string',
    example: 'uuid-string',
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật trạng thái tin cậy thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Cập nhật trạng thái tin cậy thành công' },
            result: { $ref: getSchemaPath(DeviceInfoResponseDto) }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu đầu vào không hợp lệ' })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy thiết bị' })
  async updateDeviceTrust(
    @CurrentUser() user: JwtPayload,
    @Param('id') deviceId: string,
    @Body() updateDto: UpdateDeviceTrustDto,
  ): Promise<ApiResponseDto<DeviceInfoResponseDto>> {
    const result = await this.deviceInfoService.updateDeviceTrust(
      user.id,
      deviceId,
      updateDto,
    );

    return ApiResponseDto.success(
      result,
      'Cập nhật trạng thái tin cậy thành công',
    );
  }

  @Delete(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ 
    summary: 'Xóa thiết bị',
    description: 'API này cho phép người dùng xóa một thiết bị khỏi danh sách thiết bị đã đăng nhập'
  })
  @ApiParam({
    name: 'id',
    description: 'ID của thiết bị',
    type: 'string',
    example: 'uuid-string',
  })
  @ApiResponse({
    status: 200,
    description: 'Xóa thiết bị thành công',
    schema: {
      allOf: [
        { $ref: getSchemaPath(ApiResponseDto) },
        {
          properties: {
            code: { type: 'number', example: 200 },
            message: { type: 'string', example: 'Xóa thiết bị thành công' },
            result: { type: 'null' }
          }
        }
      ]
    }
  })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy thiết bị' })
  async deleteDevice(
    @CurrentUser() user: JwtPayload,
    @Param('id') deviceId: string,
  ): Promise<ApiResponseDto<null>> {
    await this.deviceInfoService.deleteDevice(user.id, deviceId);

    return ApiResponseDto.success(
      null,
      'Xóa thiết bị thành công',
    );
  }
}
