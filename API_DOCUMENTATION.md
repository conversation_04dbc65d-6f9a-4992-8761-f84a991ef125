# API Documentation - Auth Verification Logs & Device Info

## Tổng quan

Đã tạo thành công các API để quản lý **Auth Verification Logs** (Lịch sử xác thực) và **Device Info** (Thông tin thiết bị) cho người dùng.

## 🔐 Auth Verification Logs APIs

### Base URL: `/user/auth-verification-logs`

#### 1. **GET** `/user/auth-verification-logs`
- **Mô tả**: L<PERSON><PERSON> danh sách log xác thực của người dùng hiện tại
- **Authentication**: Bear<PERSON> (JWT)
- **Query Parameters**:
  - `authMethod` (optional): Phương thức xác thực (EMAIL, SMS, GOOGLE_AUTHENTICATOR)
  - `status` (optional): Trạng thái (SUCCESS, FAILED, PENDING)
  - `ipAddress` (optional): <PERSON><PERSON><PERSON> chỉ IP
  - `fromDate` (optional): Từ ngày (timestamp)
  - `toDate` (optional): <PERSON><PERSON><PERSON> ngày (timestamp)
  - `page` (optional): <PERSON><PERSON> trang (default: 1)
  - `limit` (optional): Số bản ghi/trang (default: 10)
- **Response**: Danh sách log với phân trang

#### 2. **GET** `/user/auth-verification-logs/stats`
- **Mô tả**: Lấy thống kê log xác thực của người dùng
- **Authentication**: Bearer Token (JWT)
- **Query Parameters**:
  - `fromDate` (optional): Từ ngày (timestamp)
  - `toDate` (optional): Đến ngày (timestamp)
- **Response**: Thống kê tổng quan
  ```json
  {
    "totalAttempts": 100,
    "successfulAttempts": 95,
    "failedAttempts": 5,
    "successRate": 95.0,
    "methodStats": [
      {
        "method": "EMAIL",
        "count": 50,
        "successCount": 48
      }
    ]
  }
  ```

#### 3. **GET** `/user/auth-verification-logs/:id`
- **Mô tả**: Lấy chi tiết log xác thực theo ID
- **Authentication**: Bearer Token (JWT)
- **Path Parameters**:
  - `id`: ID của log xác thực
- **Response**: Chi tiết log xác thực

## 📱 Device Info APIs

### Base URL: `/user/devices`

#### 1. **GET** `/user/devices`
- **Mô tả**: Lấy danh sách thiết bị của người dùng hiện tại
- **Authentication**: Bearer Token (JWT)
- **Query Parameters**:
  - `browser` (optional): Tên trình duyệt
  - `operatingSystem` (optional): Hệ điều hành
  - `isTrusted` (optional): Thiết bị đáng tin cậy (true/false)
  - `ipAddress` (optional): Địa chỉ IP
  - `fromDate` (optional): Từ ngày (timestamp)
  - `toDate` (optional): Đến ngày (timestamp)
  - `page` (optional): Số trang (default: 1)
  - `limit` (optional): Số bản ghi/trang (default: 10)
- **Response**: Danh sách thiết bị với phân trang

#### 2. **GET** `/user/devices/stats`
- **Mô tả**: Lấy thống kê thiết bị của người dùng
- **Authentication**: Bearer Token (JWT)
- **Response**: Thống kê tổng quan
  ```json
  {
    "totalDevices": 5,
    "trustedDevices": 3,
    "untrustedDevices": 2,
    "browserStats": [
      {
        "browser": "Chrome",
        "count": 3
      }
    ],
    "osStats": [
      {
        "os": "Windows 10",
        "count": 2
      }
    ]
  }
  ```

#### 3. **GET** `/user/devices/:id`
- **Mô tả**: Lấy chi tiết thiết bị theo ID
- **Authentication**: Bearer Token (JWT)
- **Path Parameters**:
  - `id`: ID của thiết bị (UUID)
- **Response**: Chi tiết thiết bị

#### 4. **PUT** `/user/devices/:id/trust`
- **Mô tả**: Cập nhật trạng thái tin cậy của thiết bị
- **Authentication**: Bearer Token (JWT)
- **Path Parameters**:
  - `id`: ID của thiết bị (UUID)
- **Request Body**:
  ```json
  {
    "isTrusted": true
  }
  ```
- **Response**: Thiết bị đã cập nhật

#### 5. **DELETE** `/user/devices/:id`
- **Mô tả**: Xóa thiết bị khỏi danh sách
- **Authentication**: Bearer Token (JWT)
- **Path Parameters**:
  - `id`: ID của thiết bị (UUID)
- **Response**: Thông báo thành công

## 📊 Response Format

Tất cả API đều sử dụng format response chuẩn:

```json
{
  "code": 200,
  "message": "Success message",
  "result": {
    // Dữ liệu response
  }
}
```

Đối với API phân trang:

```json
{
  "code": 200,
  "message": "Success message",
  "result": {
    "items": [...],
    "meta": {
      "totalItems": 100,
      "itemCount": 10,
      "itemsPerPage": 10,
      "totalPages": 10,
      "currentPage": 1
    }
  }
}
```

## 🔧 Các file đã tạo

### DTOs
- `src/modules/user/dto/auth-verification-log.dto.ts`
- `src/modules/user/dto/device-info.dto.ts`

### Services
- `src/modules/user/user/service/auth-verification-log.service.ts`
- `src/modules/user/user/service/device-info.service.ts`

### Controllers
- `src/modules/user/user/controller/auth-verification-log.controller.ts`
- `src/modules/user/user/controller/device-info.controller.ts`

### Module Updates
- Đã cập nhật `src/modules/user/user.module.ts` để đăng ký các service và controller mới
- Đã cập nhật `src/modules/user/dto/index.ts` để export các DTO mới

## ✅ Tính năng đã implement

### Auth Verification Logs
- ✅ Lấy danh sách với filter và phân trang
- ✅ Lấy chi tiết theo ID
- ✅ Thống kê tổng quan
- ✅ Filter theo phương thức xác thực, trạng thái, IP, thời gian
- ✅ Swagger documentation đầy đủ

### Device Info
- ✅ Lấy danh sách với filter và phân trang
- ✅ Lấy chi tiết theo ID
- ✅ Cập nhật trạng thái tin cậy
- ✅ Xóa thiết bị
- ✅ Thống kê tổng quan
- ✅ Filter theo trình duyệt, OS, trạng thái tin cậy, IP, thời gian
- ✅ Swagger documentation đầy đủ

## 🚀 Cách sử dụng

1. **Start server**: `npm run start:dev`
2. **Truy cập Swagger**: `http://localhost:3000/api`
3. **Authenticate**: Sử dụng Bearer Token từ API login
4. **Test APIs**: Sử dụng Swagger UI để test các endpoint

## 📝 Notes

- Tất cả API đều yêu cầu authentication (JWT Bearer Token)
- Người dùng chỉ có thể xem dữ liệu của chính mình
- Validation đầy đủ cho tất cả input parameters
- Error handling với HTTP status codes chuẩn
- Logging chi tiết cho debugging
- TypeScript strict mode compliance
