# Migration: International Phone Validation

## Tổng quan
Đã tạo và áp dụng annotation validate số điện thoại chuẩn quốc tế hóa sử dụng thư viện `libphonenumber-js` để thay thế các validator cũ.

## Các thay đổi đã thực hiện

### 1. Tạo validator mới
- **File**: `src/shared/validators/international-phone.validator.ts`
- **Decorators**:
  - `@IsInternationalPhone()`: Validate số điện thoại quốc tế chuẩn
  - `@IsInternationalPhoneWithCountry()`: Validate số điện thoại với mã quốc gia riêng biệt
- **Utility functions**:
  - `formatInternationalPhone()`: Format số điện thoại
  - `getPhoneInfo()`: L<PERSON>y thông tin chi tiết về số điện thoại

### 2. Cài đặt dependencies
- <PERSON><PERSON> cài đặt thư viện `libphonenumber-js` qua npm

### 3. Cập nhật các DTO

#### Auth Module
- **File**: `src/modules/auth/dto/register.dto.ts`
- **Thay đổi**: Thay thế validation cũ bằng `@IsInternationalPhone()`

#### User Module
- **File**: `src/modules/user/dto/update-personal-info.dto.ts`
- **Thay đổi**: Thay thế `@Matches()` regex bằng `@IsInternationalPhone()`

- **File**: `src/modules/user/dto/update-business-info.dto.ts`
- **Thay đổi**: Thêm `@IsInternationalPhone()` cho businessPhone field

#### Marketing Module
- **File**: `src/modules/marketing/user/dto/audience/create-audience.dto.ts`
- **Thay đổi**: Thay thế `@IsPhoneNumber()` bằng `@IsInternationalPhone()`

- **File**: `src/modules/marketing/user/dto/audience/update-audience.dto.ts`
- **Thay đổi**: Thay thế `@IsPhoneNumber()` bằng `@IsInternationalPhone()`

- **File**: `src/modules/marketing/admin/dto/audience/audience-query.dto.ts`
- **Thay đổi**: Thay thế `@IsPhoneNumber()` bằng `@IsInternationalPhone()`

- **File**: `src/modules/marketing/admin/dto/audience/create-audience.dto.ts`
- **Thay đổi**: Thay thế `@IsPhoneNumber()` bằng `@IsInternationalPhone()`

- **File**: `src/modules/marketing/admin/dto/audience/update-audience.dto.ts`
- **Thay đổi**: Thay thế `@IsPhoneNumber()` bằng `@IsInternationalPhone()`

#### Integration Module
- **File**: `src/modules/integration/admin/dto/create-integration-audience.dto.ts`
- **Thay đổi**: Thay thế `@IsPhoneNumber()` bằng `@IsInternationalPhone()`

- **File**: `src/modules/integration/user/dto/whatsapp/send-whatsapp-message.dto.ts`
- **Thay đổi**: Thay thế `@Matches()` regex bằng `@IsInternationalPhone()`

#### Business Module
- **File**: `src/modules/business/user/dto/create-user-convert-customer.dto.ts`
- **Thay đổi**: Thay thế `@Matches()` regex bằng `@IsInternationalPhone()`

- **File**: `src/modules/business/user/dto/user-shop-info.dto.ts`
- **Thay đổi**: Thay thế `@IsPhoneNumber('VN')` bằng `@IsInternationalPhone()`

- **File**: `src/modules/business/user/dto/merge-user-convert-customer.dto.ts`
- **Thay đổi**: Thay thế `@Matches()` regex bằng `@IsInternationalPhone()`

- **File**: `src/modules/business/user/dto/update-customer-basic-info.dto.ts`
- **Thay đổi**: Thay thế `@Matches()` regex bằng `@IsInternationalPhone()`

- **File**: `src/modules/business/user/dto/update-user-convert-customer.dto.ts`
- **Thay đổi**: Thay thế `@Matches()` regex bằng `@IsInternationalPhone()`

### 4. Tạo documentation
- **File**: `src/shared/validators/README.md`
- **Nội dung**: Hướng dẫn sử dụng validator mới, ví dụ và migration guide

- **File**: `src/shared/validators/index.ts`
- **Nội dung**: Export validator để sử dụng

### 5. Tạo test file
- **File**: `src/shared/validators/international-phone.validator.spec.ts`
- **Nội dung**: Unit tests cho validator mới

### 6. Dọn dẹp code cũ
- **Xóa**: `src/modules/marketing/admin/validators/phone-with-country.validator.ts`
- **Xóa**: `src/modules/marketing/user/validators/phone-with-country.validator.ts`
- **Lý do**: Các validator cũ không còn được sử dụng

## Lợi ích của validator mới

### 1. Độ chính xác cao
- Sử dụng thư viện `libphonenumber-js` - thư viện chuẩn được Google phát triển
- Hỗ trợ validate số điện thoại của tất cả quốc gia trên thế giới
- Nhận diện được loại số điện thoại (mobile, landline, etc.)

### 2. Thống nhất
- Tất cả các API sử dụng cùng một validator
- Message lỗi thống nhất và rõ ràng
- Cách sử dụng đơn giản và nhất quán

### 3. Tính năng mở rộng
- Utility functions để format và lấy thông tin số điện thoại
- Hỗ trợ cả số điện thoại có mã quốc gia riêng biệt
- Dễ dàng mở rộng thêm tính năng mới

### 4. Hiệu suất
- Validation nhanh và hiệu quả
- Không cần kết nối mạng
- Cache kết quả validation

## Các định dạng số điện thoại được hỗ trợ

- **Việt Nam**: +84912345678, +84 91 234 5678
- **Mỹ**: +1234567890, ****** 567 890  
- **Anh**: +447911123456, +44 7911 123456
- **Trung Quốc**: +8613800138000, +86 138 0013 8000
- **Nhật Bản**: +819012345678, +81 90 1234 5678
- **Hàn Quốc**: +821012345678, +82 10 1234 5678
- **Và tất cả các quốc gia khác...**

## Cách sử dụng

### Import validator
```typescript
import { IsInternationalPhone } from '@/shared/validators';
```

### Sử dụng trong DTO
```typescript
@ApiProperty({
  description: 'Số điện thoại (định dạng quốc tế)',
  example: '+84912345678',
})
@IsString()
@IsNotEmpty()
@IsInternationalPhone({ 
  message: 'Số điện thoại không hợp lệ, phải là số điện thoại quốc tế hợp lệ (ví dụ: +84912345678)' 
})
phoneNumber: string;
```

### Sử dụng utility functions
```typescript
import { formatInternationalPhone, getPhoneInfo } from '@/shared/validators';

// Format số điện thoại
const formatted = formatInternationalPhone('+84912345678');
// Kết quả: "+84 91 234 5678"

// Lấy thông tin chi tiết
const info = getPhoneInfo('+84912345678');
// Kết quả: { isValid: true, country: 'VN', type: 'MOBILE', ... }
```

## Testing

Chạy test để kiểm tra validator:
```bash
npm test src/shared/validators/international-phone.validator.spec.ts
```

## Lưu ý quan trọng

1. **Luôn sử dụng định dạng quốc tế**: Số điện thoại phải bắt đầu bằng dấu `+` và mã quốc gia
2. **Cập nhật frontend**: Đảm bảo frontend cũng validate và format số điện thoại đúng cách
3. **Database migration**: Có thể cần cập nhật dữ liệu cũ để đảm bảo định dạng thống nhất
4. **API documentation**: Cập nhật Swagger documentation với ví dụ mới

## Kết luận

Việc migration sang validator mới đã hoàn thành thành công. Tất cả các API liên quan đến số điện thoại giờ đây sử dụng cùng một chuẩn validation quốc tế, đảm bảo tính nhất quán và độ chính xác cao trong toàn bộ hệ thống.
